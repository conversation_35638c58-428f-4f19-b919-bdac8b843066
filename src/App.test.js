import { render, screen } from '@testing-library/react';
import App from './App';

test('renders welcome message', () => {
  render(<App />);
  const welcomeElement = screen.getByText(/Welcome to your IT excellence company/i);
  expect(welcomeElement).toBeInTheDocument();
});

test('renders navigation menu', () => {
  render(<App />);
  const servicesLink = screen.getByRole('link', { name: /Services/i });
  const portfolioLink = screen.getByRole('link', { name: /Portfolio/i });
  const aboutLink = screen.getByRole('link', { name: /About/i });
  const teamLink = screen.getByRole('link', { name: /Team/i });
  const contactLink = screen.getByRole('link', { name: /Contact/i });

  expect(servicesLink).toBeInTheDocument();
  expect(portfolioLink).toBeInTheDocument();
  expect(aboutLink).toBeInTheDocument();
  expect(teamLink).toBeInTheDocument();
  expect(contactLink).toBeInTheDocument();
});

test('renders company branding', () => {
  render(<App />);
  const brandingElement = screen.getByText(/your IT company!/i);
  expect(brandingElement).toBeInTheDocument();
});
