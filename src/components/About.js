import React from 'react';

const timelineData = [
  {
    id: 1,
    period: "1990-1996",
    title: "First steps",
    description: "From <PERSON> and Cobol running a CP500 machine to Delphi with Paradox and Oracle databases.",
    image: "img/about/1.jpg",
    inverted: false
  },
  {
    id: 2,
    period: "1997-1999",
    title: "Professional Realization",
    description: "The pleasure to make customers and business happy as ever before, implementing a bespoke CRM platform.",
    image: "img/about/2.jpg",
    inverted: true
  },
  {
    id: 3,
    period: "2000-2005",
    title: "Distributed Applications and Web revolution",
    description: "Corba, Dcom, RPC and Java. Build bespoke frameworks and consolidation of SOLID principles and Clean architecture.",
    image: "img/about/3.jpg",
    inverted: false
  },
  {
    id: 4,
    period: "2006-2017",
    title: "Design",
    description: "Thousands of applications bouncing inside an enterprise landscape. Strategic views combined with roadmaps can lead you to a successful business avoiding all pitfalls of a growing business.",
    image: "img/about/4.jpg",
    inverted: true
  },
  {
    id: 5,
    period: "2017",
    title: "Cloud Evolution",
    description: "We are cloud specialists, if you are thinking on migrating, adopt fully managed cloud services, go serverless and, create and build your cloud infrastructure talk to us. Embrace the time-to-market, velocity, elasticity, resilience and provision of resourses that was never possible before.",
    image: "img/about/5.jpeg",
    inverted: false
  },
  {
    id: 6,
    period: "2021",
    title: "AI and Web3 - Smart Contracts, NFTs, Wallet...",
    description: "We help customers to take advantage of AI on their existing business offers and new opportunities. Web3 represents the evolution of how technology and data are used, leading to a wave of disruptive innovation across the globe, our aim is help customers in this new road.",
    image: "img/about/web-3.0.png",
    inverted: false
  }
];

const TimelineItem = ({ item }) => {
  return (
    <li className={item.inverted ? "timeline-inverted" : ""}>
      <div className="timeline-image">
        <img
          className="rounded-circle img-fluid"
          src={item.image}
          alt={item.title}
        />
      </div>
      <div className="timeline-panel">
        <div className="timeline-heading">
          <h4>{item.period}</h4>
          <h4 className="subheading">{item.title}</h4>
        </div>
        <div className="timeline-body">
          <p className="text-muted">{item.description}</p>
        </div>
      </div>
    </li>
  );
};

const About = () => {
  return (
    <section id="about">
      <div className="container">
        <div className="row">
          <div className="col-lg-12 text-center">
            <h2 className="section-heading">About</h2>
            <h3 className="section-subheading text-muted">
              Some information about our journey so far
            </h3>
          </div>
        </div>
        <div className="row">
          <div className="col-lg-12">
            <ul className="timeline">
              {timelineData.map(item => (
                <TimelineItem key={item.id} item={item} />
              ))}
              <li className="timeline-inverted">
                <div className="timeline-image">
                  <h4>
                    Be Part
                    <br />
                    Of Our
                    <br />
                    Story!
                  </h4>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
