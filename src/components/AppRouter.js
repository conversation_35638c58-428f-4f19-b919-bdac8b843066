import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './Navigation';
import Footer from './Footer';
import PortfolioModals from './PortfolioModals';

// Page components
import HomePage from '../pages/HomePage';
import ServicesPage from '../pages/ServicesPage';
import PortfolioPage from '../pages/PortfolioPage';
import AboutPage from '../pages/AboutPage';
import TeamPage from '../pages/TeamPage';
import ContactPage from '../pages/ContactPage';

const AppRouter = () => {
  return (
    <Router>
      <div id="page-top">
        <Navigation />
        
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/services" element={<ServicesPage />} />
          <Route path="/portfolio" element={<PortfolioPage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/team" element={<TeamPage />} />
          <Route path="/contact" element={<ContactPage />} />
        </Routes>
        
        <Footer />
        <PortfolioModals />
      </div>
    </Router>
  );
};

export default AppRouter;
