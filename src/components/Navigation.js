import React from 'react';

const Navigation = () => {
  return (
    <nav
      className="navbar fixed-top navbar-expand-md navbar-inverse"
      id="mainNav"
    >
      <div className="container">
        <button
          className="navbar-toggler navbar-toggler-right"
          type="button"
          data-toggle="collapse"
          data-target="#navbarResponsive"
          aria-controls="navbarResponsive"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          Menu <i className="fa fa-bars"></i>
        </button>
        <img src="img/logo.png" id="logo" alt="" />
        <a className="navbar-brand page-scroll" href="#page-top">
          &nbsp;&nbsp;&nbsp;&nbsp;your IT company!
        </a>
        <div className="collapse navbar-collapse" id="navbarResponsive">
          <ul className="navbar-nav ml-auto">
            <li className="nav-item">
              <a className="nav-link page-scroll" href="#services">
                Services&nbsp;
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link page-scroll" href="#portfolio">
                Portfolio&nbsp;
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link page-scroll" href="#about">
                About&nbsp;
              </a>
            </li>
            <li className="nav-item">
              <a className="nav-link page-scroll" href="#team">
                Team&nbsp;
              </a>
            </li>
            <li className="nav-item">
              <a
                className="nav-link page-scroll navbar-toggler-right"
                href="#contact"
              >
                Contact&nbsp;
              </a>
            </li>
          </ul>
        </div>
        <button className="btn btn-xs" type="button" id="btnLogIn">
          Log in<i className="fa fa-bars"></i>
        </button>
      </div>
    </nav>
  );
};

export default Navigation;
