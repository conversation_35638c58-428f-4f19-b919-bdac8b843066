import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = () => {
  const location = useLocation();

  const isActive = (path) => {
    if (path === '/' && location.pathname === '/') return true;
    if (path !== '/' && location.pathname.startsWith(path)) return true;
    return false;
  };

  return (
    <nav
      className="navbar fixed-top navbar-expand-md navbar-inverse"
      id="mainNav"
    >
      <div className="container">
        <button
          className="navbar-toggler navbar-toggler-right"
          type="button"
          data-toggle="collapse"
          data-target="#navbarResponsive"
          aria-controls="navbarResponsive"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          Menu <i className="fa fa-bars"></i>
        </button>
        <img src="img/logo.png" id="logo" alt="Inetpub Logo" />
        <Link className="navbar-brand page-scroll" to="/">
          &nbsp;&nbsp;&nbsp;&nbsp;your IT company!
        </Link>
        <div className="collapse navbar-collapse" id="navbarResponsive">
          <ul className="navbar-nav ml-auto">
            <li className="nav-item">
              <Link
                className={`nav-link ${isActive('/services') ? 'active' : ''}`}
                to="/services"
              >
                Services&nbsp;
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link ${isActive('/portfolio') ? 'active' : ''}`}
                to="/portfolio"
              >
                Portfolio&nbsp;
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link ${isActive('/about') ? 'active' : ''}`}
                to="/about"
              >
                About&nbsp;
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link ${isActive('/team') ? 'active' : ''}`}
                to="/team"
              >
                Team&nbsp;
              </Link>
            </li>
            <li className="nav-item">
              <Link
                className={`nav-link navbar-toggler-right ${isActive('/contact') ? 'active' : ''}`}
                to="/contact"
              >
                Contact&nbsp;
              </Link>
            </li>
          </ul>
        </div>
        <button className="btn btn-xs" type="button" id="btnLogIn">
          Log in<i className="fa fa-bars"></i>
        </button>
      </div>
    </nav>
  );
};

export default Navigation;
