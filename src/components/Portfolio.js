import React from 'react';

const portfolioItems = [
  {
    id: 1,
    image: "img/portfolio/reactive-manifesto.png",
    title: "Responsive Applications",
    subtitle: "Reactive Manifesto"
  },
  {
    id: 2,
    image: "img/portfolio/ms-architecture.png",
    title: "Micro-services Architecture",
    subtitle: "Re-design your landscape"
  },
  {
    id: 3,
    image: "img/portfolio/api-revolution.png",
    title: "API Revolution",
    subtitle: "Service Maturity"
  },
  {
    id: 4,
    image: "img/portfolio/event-sourcing.png",
    title: "Event Sourcing",
    subtitle: "Do more with your data"
  },
  {
    id: 5,
    image: "img/portfolio/clean-architecture.png",
    title: "Clean Architecture",
    subtitle: "No more overengineering"
  },
  {
    id: 6,
    image: "img/portfolio/bring-it-on.png",
    title: "Tibco, Containers, Cloud, AWS...",
    subtitle: "Bring it on..."
  }
];

const PortfolioItem = ({ item }) => {
  return (
    <div className="col-md-4 col-sm-6 portfolio-item">
      <div className="portfolio-hover">
        <div className="portfolio-hover-content">
          <i className="fa fa-plus fa-3x"></i>
        </div>
      </div>
      <img
        src={item.image}
        className="img-fluid"
        alt={item.title}
      />
      <div className="portfolio-caption">
        <h4>{item.title}</h4>
        <p className="text-muted">{item.subtitle}</p>
      </div>
    </div>
  );
};

const Portfolio = () => {
  return (
    <section id="portfolio" className="bg-faded">
      <div className="container">
        <div className="row">
          <div className="col-lg-12 text-center">
            <h2 className="section-heading">Portfolio</h2>
            <h3 className="section-subheading text-muted">
              Areas of expertise
            </h3>
          </div>
        </div>
        <div className="row">
          {portfolioItems.map(item => (
            <PortfolioItem key={item.id} item={item} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
