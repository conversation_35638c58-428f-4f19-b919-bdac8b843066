import React from 'react';

const Services = () => {
  return (
    <section id="services">
      <div className="container">
        <div className="row">
          <div className="col-lg-12 text-center">
            <h2 className="section-heading">Services</h2>
            <h3 className="section-subheading text-muted">
              IT Architecture in all levels
            </h3>
          </div>
        </div>
        <div className="row text-center">
          <div className="col-md-4">
            <span className="fa-stack fa-4x">
              <i className="fa fa-circle fa-stack-2x text-primary"></i>
              <i className="fa fa-shopping-cart fa-stack-1x fa-inverse"></i>
            </span>
            <h4 className="service-heading">Consultancy</h4>
            <p className="text-muted">
              We are very experienced in several industry segments, e.g.
              banking, retail, ERP, insurance, telecom...
            </p>
          </div>
          <div className="col-md-4">
            <span className="fa-stack fa-4x">
              <i className="fa fa-circle fa-stack-2x text-primary"></i>
              <i className="fa fa-laptop fa-stack-1x fa-inverse"></i>
            </span>
            <h4 className="service-heading">Design on demand</h4>
            <p className="text-muted">
              We assess your business cases building a technical architecture
              designed and tailored based on your requirements supporting all
              life project cycles, even your governance process.
            </p>
          </div>
          <div className="col-md-4">
            <span className="fa-stack fa-4x">
              <i className="fa fa-circle fa-stack-2x text-primary"></i>
              <i className="fa fa-lock fa-stack-1x fa-inverse"></i>
            </span>
            <h4 className="service-heading">Bespoke solutions</h4>
            <p className="text-muted">
              Our solutions are created following the design principles, clean
              and simple architecture that suits your use cases targeting a
              very most desirable growing business.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
