import React from 'react';

const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Director",
    image: "img/team/1.jpg",
    socialLinks: {
      twitter: "#",
      facebook: "#",
      linkedin: "#"
    }
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Director",
    image: "img/team/2.jpg",
    socialLinks: {
      twitter: "#",
      facebook: "#",
      linkedin: "#"
    }
  },
  {
    id: 3,
    name: "<PERSON>",
    role: "Consultant",
    image: "img/team/3.jpg",
    isOpportunity: true
  }
];

const TeamMember = ({ member }) => {
  return (
    <div className="col-sm-4">
      <div className="team-member">
        <img
          src={member.image}
          className="mx-auto rounded-circle"
          alt={member.name}
        />
        <h4>{member.name}</h4>
        <p className="text-muted">{member.role}</p>
        
        {member.isOpportunity ? (
          <button
            type="button"
            className="btn btn-primary"
            data-toggle="modal"
            data-target="#portfolioModal1"
          >
            Opportunities
          </button>
        ) : (
          <ul className="list-inline social-buttons">
            <li className="list-inline-item">
              {/* <a href={member.socialLinks.twitter}><i className="fa fa-twitter"></i></a> */}
            </li>
            <li className="list-inline-item">
              {/* <a href={member.socialLinks.facebook}><i className="fa fa-facebook"></i></a> */}
            </li>
            <li className="list-inline-item">
              {/* <a href={member.socialLinks.linkedin}><i className="fa fa-linkedin"></i></a> */}
            </li>
          </ul>
        )}
      </div>
    </div>
  );
};

const Team = () => {
  return (
    <section id="team" className="bg-faded">
      <div className="container">
        <div className="row">
          <div className="col-lg-12 text-center">
            <h2 className="section-heading">Our Amazing Team</h2>
          </div>
        </div>
        <div className="row">
          {teamMembers.map(member => (
            <TeamMember key={member.id} member={member} />
          ))}
        </div>
        <div className="row">
          <div className="col-lg-8 offset-lg-2 text-center">
            <p className="large text-muted">
              "Everyone is a prisoner of his own experiences." Edward R.
              Murrow.
            </p>
            <p className="large text-muted">
              Let's be free and have more experiences!
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Team;
