import React from 'react';
import { Helmet } from 'react-helmet-async';
import Header from '../components/Header';
import Services from '../components/Services';
import Portfolio from '../components/Portfolio';
import About from '../components/About';
import Team from '../components/Team';
import Contact from '../components/Contact';

const HomePage = () => {
  return (
    <>
      <Helmet>
        <title>Inetpub - Your IT Excellence Company | IT Consultancy & Solutions</title>
        <meta name="description" content="Inetpub Ltd provides expert IT consultancy, architecture design, and bespoke solutions. Specializing in cloud, microservices, AI, and Web3 technologies." />
        <meta name="keywords" content="IT consultancy, software architecture, cloud solutions, microservices, AI, Web3, React development" />
        <meta property="og:title" content="Inetpub - Your IT Excellence Company" />
        <meta property="og:description" content="Expert IT consultancy and bespoke solutions for modern businesses" />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="/" />
      </Helmet>
      
      <Header />
      <Services />
      <Portfolio />
      <About />
      <Team />
      <Contact />
    </>
  );
};

export default HomePage;
