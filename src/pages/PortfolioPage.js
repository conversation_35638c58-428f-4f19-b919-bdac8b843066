import React from 'react';
import { Helmet } from 'react-helmet-async';
import Portfolio from '../components/Portfolio';

const PortfolioPage = () => {
  return (
    <>
      <Helmet>
        <title>Portfolio - Areas of Expertise | Inetpub</title>
        <meta name="description" content="Explore our expertise in responsive applications, microservices architecture, API development, event sourcing, clean architecture, and cloud technologies." />
        <meta name="keywords" content="portfolio, microservices, API development, event sourcing, clean architecture, cloud computing, reactive manifesto" />
        <meta property="og:title" content="Portfolio - Inetpub" />
        <meta property="og:description" content="Our areas of technical expertise and specialization" />
        <link rel="canonical" href="/portfolio" />
      </Helmet>
      
      <div style={{ paddingTop: '100px' }}>
        <Portfolio />
      </div>
    </>
  );
};

export default PortfolioPage;
